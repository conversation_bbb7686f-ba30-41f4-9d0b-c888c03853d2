// Heart Disease Prediction Achievement Content
export const heartDiseasePredictionContent = {
    id: "heart-disease-prediction",
    title: "<PERSON><PERSON><PERSON>i Nhì Cấp Tỉnh - Hệ Thống Dự Đoán Bệnh Tim Dựa Trên Điện Tâm Đồ",
    date: "Năm 2023",
    excerpt: "<PERSON>ệ thống AI tiên tiến sử dụng deep learning để phân tích điện tâm đồ và dự đoán nguy cơ bệnh tim với độ chính xác cao, hỗ trợ chẩn đoán sớm cho bác sĩ.",
    images: [
        "../assets/images/achievements/heart-disease-prediction-1.jpg",
        "../assets/images/achievements/heart-disease-prediction-2.jpg",
        "../assets/images/achievements/heart-disease-prediction-3.jpg"
    ],
    content: `
        <p><PERSON>ệ thống dự đoán bệnh tim dựa trên điện tâm đồ (ECG) là một dự án nghiên cứu ứng dụng trí tuệ nhân tạo trong y tế, đã đạt giải <PERSON>ì cấp tỉnh trong cuộc thi Khoa học <PERSON>ỹ thuật năm 2023.</p>

        <p>Bệnh tim mạch là nguyên nhân gây tử vong hàng đầu trên thế giới. Việc chẩn đoán sớm và chính xác các bệnh lý tim mạch có ý nghĩa quyết định đến hiệu quả điều trị và tính mạng của bệnh nhân. Điện tâm đồ (ECG) là một trong những phương pháp chẩn đoán quan trọng nhất, tuy nhiên việc đọc và phân tích ECG đòi hỏi kinh nghiệm và chuyên môn cao.</p>

        <p><strong>Công nghệ và phương pháp:</strong></p>
        <ul>
            <li>Sử dụng mạng nơ-ron tích chập (CNN) để phân tích tín hiệu ECG</li>
            <li>Áp dụng kỹ thuật Deep Learning với TensorFlow và Keras</li>
            <li>Xử lý tín hiệu số để lọc nhiễu và chuẩn hóa dữ liệu</li>
            <li>Tích hợp thuật toán Machine Learning để tối ưu độ chính xác</li>
            <li>Phát triển giao diện web thân thiện cho bác sĩ sử dụng</li>
        </ul>

        <p><strong>Tính năng chính của hệ thống:</strong></p>
        <ul>
            <li>Phân tích tự động tín hiệu ECG trong thời gian thực</li>
            <li>Dự đoán nguy cơ các bệnh lý tim mạch phổ biến</li>
            <li>Cảnh báo sớm các dấu hiệu bất thường</li>
            <li>Tạo báo cáo chi tiết với độ tin cậy cao</li>
            <li>Lưu trữ và quản lý lịch sử bệnh án</li>
            <li>Hỗ trợ đa ngôn ngữ và chuẩn y tế quốc tế</li>
        </ul>

        <p><strong>Kết quả đạt được:</strong></p>
        <ul>
            <li>Độ chính xác dự đoán đạt 94.7% trên tập dữ liệu test</li>
            <li>Thời gian phân tích chỉ 2-3 giây cho mỗi ECG</li>
            <li>Giảm 60% thời gian chẩn đoán so với phương pháp truyền thống</li>
            <li>Hỗ trợ phát hiện sớm 15 loại bệnh lý tim mạch khác nhau</li>
        </ul>

        <p>Dự án được phát triển bằng Python với các thư viện chuyên dụng như TensorFlow, NumPy, và Matplotlib. Giao diện người dùng được xây dựng bằng Flask và Bootstrap, đảm bảo tính thân thiện và dễ sử dụng cho đội ngũ y tế.</p>

        <p>Hệ thống đã được thử nghiệm tại một số bệnh viện địa phương và nhận được phản hồi tích cực từ các bác sĩ tim mạch. Độ chính xác cao và tốc độ xử lý nhanh của hệ thống giúp hỗ trợ đắc lực cho việc chẩn đoán, đặc biệt hữu ích tại các cơ sở y tế thiếu chuyên gia tim mạch.</p>

        <p>Thành tích giải Nhì cấp tỉnh này không chỉ khẳng định năng lực nghiên cứu và ứng dụng công nghệ AI trong y tế, mà còn mở ra hướng phát triển mới cho việc ứng dụng trí tuệ nhân tạo vào chăm sóc sức khỏe cộng đồng.</p>
    `
};
