<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Th<PERSON><PERSON> - <PERSON>on</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }

        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }

        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }

        .achievements-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin: 30px 0 50px;
        }

        .achievement-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
        }

        .achievement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .achievement-image {
            height: 220px;
            overflow: hidden;
            position: relative;
        }

        .achievement-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .achievement-card:hover .achievement-image img {
            transform: scale(1.05);
        }

        .achievement-details {
            padding: 25px;
        }

        .achievement-date {
            color: #4285F4;
            font-size: 0.9rem;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .achievement-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 12px;
            font-weight: 700;
            line-height: 1.3;
        }

        .achievement-excerpt {
            color: #666;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
        }

        .achievement-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .stat-item {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 0.9rem;
        }

        .stat-item i {
            margin-right: 6px;
            font-size: 1rem;
        }

        .like-btn {
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #666;
            font-size: 0.9rem;
            transition: color 0.3s;
            padding: 5px 10px;
            border-radius: 20px;
            transition: all 0.3s;
        }

        .like-btn:hover {
            background-color: #f8f9fa;
            color: #4285F4;
        }

        .like-btn.liked {
            color: #e74c3c;
        }

        .like-btn.liked i {
            color: #e74c3c;
        }

        .like-btn i {
            margin-right: 6px;
            font-size: 1rem;
            transition: color 0.3s;
        }

        .read-more {
            color: #4285F4;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            padding: 8px 0;
        }

        .read-more i {
            margin-left: 5px;
            transition: transform 0.3s;
        }

        .read-more:hover i {
            transform: translateX(3px);
        }

        .loading-spinner {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading-spinner i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-achievements {
            grid-column: 1/-1;
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .no-achievements i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #ddd;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .modal.active {
            display: block;
            opacity: 1;
        }

        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 30px;
            border-radius: 15px;
            width: 85%;
            max-width: 900px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(-50px);
            opacity: 0;
            transition: all 0.4s;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal.active .modal-content {
            transform: translateY(0);
            opacity: 1;
        }

        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            transition: color 0.3s;
            z-index: 10;
            background: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .close-modal:hover {
            color: #333;
            background: #f8f9fa;
        }

        .modal-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .modal-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .modal-image:hover {
            transform: scale(1.02);
        }

        .modal-header {
            margin-bottom: 25px;
        }

        .modal-title {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #333;
            line-height: 1.3;
        }

        .modal-date {
            color: #4285F4;
            font-size: 1rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .modal-stats {
            display: flex;
            gap: 25px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .modal-stat {
            display: flex;
            align-items: center;
            color: #666;
        }

        .modal-stat i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .modal-like-btn {
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-weight: 500;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .modal-like-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }

        .modal-like-btn.liked {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .modal-like-btn i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .modal-content p {
            margin-bottom: 15px;
            line-height: 1.7;
            color: #444;
        }

        .modal-content ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .modal-content li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: #444;
        }

        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                padding: 20px;
                margin: 5% auto;
            }

            .modal-title {
                font-size: 1.5rem;
            }

            .modal-stats {
                flex-direction: column;
                gap: 10px;
            }

            .modal-images {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="index.html" class="active">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Achievements Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Thành Tích Học Viên</h1>
                <p>Tự hào về những thành tựu xuất sắc của học viên Vthon trong lĩnh vực lập trình Python và AI</p>
            </div>
            
            <div class="achievements-container" id="achievementsContainer">
                <!-- Loading spinner -->
                <div class="loading-spinner" id="loadingSpinner">
                    <i class="fas fa-spinner"></i>
                    <p>Đang tải thành tích...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for Achievement Details -->
    <div class="modal" id="achievementModal">
        <div class="modal-content">
            <span class="close-modal"><i class="fas fa-times"></i></span>
            <div class="modal-header">
                <h2 class="modal-title"></h2>
                <div class="modal-date"></div>
                <div class="modal-stats">
                    <div class="modal-stat">
                        <i class="fas fa-eye"></i>
                        <span id="modalViewCount">0</span> lượt xem
                    </div>
                    <div class="modal-stat">
                        <i class="fas fa-heart"></i>
                        <span id="modalLikeCount">0</span> lượt thích
                    </div>
                </div>
                <button class="modal-like-btn" id="modalLikeBtn">
                    <i class="fas fa-heart"></i>
                    <span>Thích bài viết</span>
                </button>
            </div>
            <div class="modal-images" id="modalImages"></div>
            <div class="modal-body" id="modalBody"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, setDoc, updateDoc, increment, arrayUnion, arrayRemove } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Global variables
        let currentUser = null;
        let currentAchievementId = null;

        // Achievement data structure
        const achievementsData = {
            "covid-app": {
                id: "covid-app",
                title: "Giải Nhất Cuộc Thi KHKT Tỉnh Kon Tum - Ứng Dụng Hỗ Trợ Điều Trị COVID-19",
                date: "Năm 2021",
                excerpt: "Dự án ứng dụng Android hỗ trợ bệnh nhân và bác sĩ trong việc điều trị COVID-19 đã đạt giải Nhất cấp tỉnh và tham gia cuộc thi KHKT quốc gia.",
                images: ["../assets/images/achievements/covid-app-award.jpg"],
                content: `
                    <p>Dự án ứng dụng Android hỗ trợ bệnh nhân và bác sĩ trong việc điều trị COVID-19 đã đạt giải Nhất cấp tỉnh Kon Tum và được tham gia cuộc thi Khoa học Kỹ thuật cấp quốc gia.</p>

                    <p>Trong bối cảnh đại dịch COVID-19 diễn ra phức tạp, việc quản lý và theo dõi tình trạng sức khỏe của bệnh nhân trở nên vô cùng quan trọng. Ứng dụng được phát triển nhằm tạo ra một cầu nối hiệu quả giữa bệnh nhân và đội ngũ y tế, giúp tối ưu hóa quá trình điều trị và theo dõi sức khỏe.</p>

                    <p><strong>Tính năng chính của ứng dụng:</strong></p>
                    <ul>
                        <li>Theo dõi các chỉ số sức khỏe hàng ngày (nhiệt độ, SpO2, nhịp tim)</li>
                        <li>Nhắc nhở uống thuốc và thực hiện các biện pháp điều trị</li>
                        <li>Kết nối trực tiếp với bác sĩ qua video call và chat</li>
                        <li>Cảnh báo tự động khi có dấu hiệu bất thường</li>
                        <li>Quản lý hồ sơ bệnh án điện tử</li>
                        <li>Cung cấp thông tin y tế đáng tin cậy về COVID-19</li>
                    </ul>

                    <p>Ứng dụng được phát triển bằng Android Studio với Java, tích hợp Firebase để lưu trữ dữ liệu và WebRTC cho tính năng video call. Giao diện được thiết kế thân thiện, dễ sử dụng cho mọi lứa tuổi, đặc biệt quan tâm đến người cao tuổi - đối tượng dễ bị tổn thương nhất trong đại dịch.</p>

                    <p>Ban giám khảo cuộc thi KHKT tỉnh Kon Tum đánh giá cao tính thực tiễn và khả năng ứng dụng rộng rãi của dự án. Ứng dụng không chỉ hỗ trợ trong giai đoạn đại dịch mà còn có thể mở rộng cho việc quản lý sức khỏe tổng quát.</p>

                    <p>Sau khi đạt giải Nhất cấp tỉnh, dự án đã được đề cử tham gia cuộc thi Khoa học Kỹ thuật cấp quốc gia, nơi có cơ hội giao lưu và học hỏi từ các dự án xuất sắc khác trên toàn quốc.</p>

                    <p>Thành tích này không chỉ là niềm tự hào cá nhân mà còn thể hiện tinh thần sáng tạo và ứng dụng công nghệ vào việc giải quyết các vấn đề thực tiễn của xã hội, đặc biệt trong những thời điểm khó khăn như đại dịch.</p>
                `
            },
            "heart-disease-prediction": {
                id: "heart-disease-prediction",
                title: "Giải Nhì Cấp Tỉnh - Hệ Thống Dự Đoán Bệnh Tim Dựa Trên Điện Tâm Đồ",
                date: "Năm 2023",
                excerpt: "Hệ thống AI tiên tiến sử dụng deep learning để phân tích điện tâm đồ và dự đoán nguy cơ bệnh tim với độ chính xác cao, hỗ trợ chẩn đoán sớm cho bác sĩ.",
                images: [
                    "../assets/images/achievements/heart-disease-prediction-1.jpg",
                    "../assets/images/achievements/heart-disease-prediction-2.jpg",
                    "../assets/images/achievements/heart-disease-prediction-3.jpg"
                ],
                content: `
                    <p>Hệ thống dự đoán bệnh tim dựa trên điện tâm đồ (ECG) là một dự án nghiên cứu ứng dụng trí tuệ nhân tạo trong y tế, đã đạt giải Nhì cấp tỉnh trong cuộc thi Khoa học Kỹ thuật năm 2023.</p>

                    <p>Bệnh tim mạch là nguyên nhân gây tử vong hàng đầu trên thế giới. Việc chẩn đoán sớm và chính xác các bệnh lý tim mạch có ý nghĩa quyết định đến hiệu quả điều trị và tính mạng của bệnh nhân. Điện tâm đồ (ECG) là một trong những phương pháp chẩn đoán quan trọng nhất, tuy nhiên việc đọc và phân tích ECG đòi hỏi kinh nghiệm và chuyên môn cao.</p>

                    <p><strong>Công nghệ và phương pháp:</strong></p>
                    <ul>
                        <li>Sử dụng mạng nơ-ron tích chập (CNN) để phân tích tín hiệu ECG</li>
                        <li>Áp dụng kỹ thuật Deep Learning với TensorFlow và Keras</li>
                        <li>Xử lý tín hiệu số để lọc nhiễu và chuẩn hóa dữ liệu</li>
                        <li>Tích hợp thuật toán Machine Learning để tối ưu độ chính xác</li>
                        <li>Phát triển giao diện web thân thiện cho bác sĩ sử dụng</li>
                    </ul>

                    <p><strong>Tính năng chính của hệ thống:</strong></p>
                    <ul>
                        <li>Phân tích tự động tín hiệu ECG trong thời gian thực</li>
                        <li>Dự đoán nguy cơ các bệnh lý tim mạch phổ biến</li>
                        <li>Cảnh báo sớm các dấu hiệu bất thường</li>
                        <li>Tạo báo cáo chi tiết với độ tin cậy cao</li>
                        <li>Lưu trữ và quản lý lịch sử bệnh án</li>
                        <li>Hỗ trợ đa ngôn ngữ và chuẩn y tế quốc tế</li>
                    </ul>

                    <p><strong>Kết quả đạt được:</strong></p>
                    <ul>
                        <li>Độ chính xác dự đoán đạt 94.7% trên tập dữ liệu test</li>
                        <li>Thời gian phân tích chỉ 2-3 giây cho mỗi ECG</li>
                        <li>Giảm 60% thời gian chẩn đoán so với phương pháp truyền thống</li>
                        <li>Hỗ trợ phát hiện sớm 15 loại bệnh lý tim mạch khác nhau</li>
                    </ul>

                    <p>Dự án được phát triển bằng Python với các thư viện chuyên dụng như TensorFlow, NumPy, và Matplotlib. Giao diện người dùng được xây dựng bằng Flask và Bootstrap, đảm bảo tính thân thiện và dễ sử dụng cho đội ngũ y tế.</p>

                    <p>Hệ thống đã được thử nghiệm tại một số bệnh viện địa phương và nhận được phản hồi tích cực từ các bác sĩ tim mạch. Độ chính xác cao và tốc độ xử lý nhanh của hệ thống giúp hỗ trợ đắc lực cho việc chẩn đoán, đặc biệt hữu ích tại các cơ sở y tế thiếu chuyên gia tim mạch.</p>

                    <p>Thành tích giải Nhì cấp tỉnh này không chỉ khẳng định năng lực nghiên cứu và ứng dụng công nghệ AI trong y tế, mà còn mở ra hướng phát triển mới cho việc ứng dụng trí tuệ nhân tạo vào chăm sóc sức khỏe cộng đồng.</p>
                `
            }
        };

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            onAuthStateChanged(auth, (user) => {
                currentUser = user;
                loadAchievements();
            });
        });

        // Load and display achievements
        async function loadAchievements() {
            const container = document.getElementById('achievementsContainer');
            const loadingSpinner = document.getElementById('loadingSpinner');

            try {
                // Clear container
                container.innerHTML = '';

                // Show loading
                container.appendChild(loadingSpinner);

                // Load achievements with stats
                const achievementElements = [];

                for (const [id, achievement] of Object.entries(achievementsData)) {
                    const stats = await getAchievementStats(id);
                    const element = createAchievementCard(achievement, stats);
                    achievementElements.push(element);
                }

                // Remove loading and add achievements
                container.innerHTML = '';
                achievementElements.forEach(element => container.appendChild(element));

                if (achievementElements.length === 0) {
                    container.innerHTML = `
                        <div class="no-achievements">
                            <i class="fas fa-trophy"></i>
                            <h3>Chưa có thành tích nào</h3>
                            <p>Thành tích sẽ được cập nhật sớm</p>
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error loading achievements:', error);
                container.innerHTML = `
                    <div class="no-achievements">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Lỗi tải dữ liệu</h3>
                        <p>Không thể tải thành tích. Vui lòng thử lại sau.</p>
                    </div>
                `;
            }
        }

        // Create achievement card element
        function createAchievementCard(achievement, stats) {
            const card = document.createElement('div');
            card.className = 'achievement-card';
            card.dataset.id = achievement.id;

            const isLiked = currentUser && stats.likedBy && stats.likedBy.includes(currentUser.uid);

            card.innerHTML = `
                <div class="achievement-image">
                    <img src="${achievement.images[0]}" alt="${achievement.title}">
                </div>
                <div class="achievement-details">
                    <div class="achievement-date">${achievement.date}</div>
                    <h3 class="achievement-title">${achievement.title}</h3>
                    <p class="achievement-excerpt">${achievement.excerpt}</p>
                    <div class="achievement-stats">
                        <div class="stat-item">
                            <i class="fas fa-eye"></i>
                            <span>${stats.views || 0}</span>
                        </div>
                        <button class="like-btn ${isLiked ? 'liked' : ''}" onclick="toggleLike('${achievement.id}', event)">
                            <i class="fas fa-heart"></i>
                            <span>${stats.likes || 0}</span>
                        </button>
                    </div>
                    <a href="#" class="read-more" onclick="openAchievementModal('${achievement.id}', event)">
                        Xem thêm <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            `;

            return card;
        }

        // Get achievement statistics from Firebase
        async function getAchievementStats(achievementId) {
            try {
                const docRef = doc(db, "achievements", achievementId);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    return docSnap.data();
                } else {
                    // Initialize if doesn't exist
                    const initialStats = {
                        views: 0,
                        likes: 0,
                        likedBy: [],
                        createdAt: new Date().toISOString()
                    };
                    await setDoc(docRef, initialStats);
                    return initialStats;
                }
            } catch (error) {
                console.error('Error getting achievement stats:', error);
                return { views: 0, likes: 0, likedBy: [] };
            }
        }

        // Toggle like for achievement
        window.toggleLike = async function(achievementId, event) {
            event.preventDefault();
            event.stopPropagation();

            if (!currentUser) {
                alert('Bạn cần đăng nhập để thích bài viết');
                return;
            }

            try {
                const docRef = doc(db, "achievements", achievementId);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const data = docSnap.data();
                    const likedBy = data.likedBy || [];
                    const isLiked = likedBy.includes(currentUser.uid);

                    if (isLiked) {
                        // Unlike
                        await updateDoc(docRef, {
                            likes: increment(-1),
                            likedBy: arrayRemove(currentUser.uid)
                        });
                    } else {
                        // Like
                        await updateDoc(docRef, {
                            likes: increment(1),
                            likedBy: arrayUnion(currentUser.uid)
                        });
                    }

                    // Refresh achievements
                    loadAchievements();
                }
            } catch (error) {
                console.error('Error toggling like:', error);
                alert('Có lỗi xảy ra. Vui lòng thử lại.');
            }
        };

        // Open achievement modal
        window.openAchievementModal = async function(achievementId, event) {
            event.preventDefault();

            currentAchievementId = achievementId;
            const achievement = achievementsData[achievementId];
            const modal = document.getElementById('achievementModal');

            // Increment view count
            await incrementViewCount(achievementId);

            // Get updated stats
            const stats = await getAchievementStats(achievementId);

            // Update modal content
            document.querySelector('.modal-title').textContent = achievement.title;
            document.querySelector('.modal-date').textContent = achievement.date;
            document.getElementById('modalViewCount').textContent = stats.views || 0;
            document.getElementById('modalLikeCount').textContent = stats.likes || 0;
            document.getElementById('modalBody').innerHTML = achievement.content;

            // Update images
            const modalImages = document.getElementById('modalImages');
            modalImages.innerHTML = '';
            achievement.images.forEach(imageSrc => {
                const img = document.createElement('img');
                img.src = imageSrc;
                img.alt = achievement.title;
                img.className = 'modal-image';
                img.onclick = () => openImageFullscreen(imageSrc);
                modalImages.appendChild(img);
            });

            // Update like button
            const modalLikeBtn = document.getElementById('modalLikeBtn');
            const isLiked = currentUser && stats.likedBy && stats.likedBy.includes(currentUser.uid);

            modalLikeBtn.className = `modal-like-btn ${isLiked ? 'liked' : ''}`;
            modalLikeBtn.innerHTML = `
                <i class="fas fa-heart"></i>
                <span>${isLiked ? 'Đã thích' : 'Thích bài viết'}</span>
            `;

            if (!currentUser) {
                modalLikeBtn.innerHTML = `
                    <i class="fas fa-heart"></i>
                    <span>Đăng nhập để thích</span>
                `;
            }

            // Show modal
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        };

        // Increment view count
        async function incrementViewCount(achievementId) {
            try {
                const docRef = doc(db, "achievements", achievementId);
                await updateDoc(docRef, {
                    views: increment(1),
                    lastViewed: new Date().toISOString()
                });
            } catch (error) {
                console.error('Error incrementing view count:', error);
            }
        }

        // Modal like button handler
        document.getElementById('modalLikeBtn').addEventListener('click', function() {
            if (currentAchievementId) {
                toggleLike(currentAchievementId, { preventDefault: () => {}, stopPropagation: () => {} });
            }
        });

        // Close modal functionality
        document.querySelector('.close-modal').addEventListener('click', closeModal);
        document.getElementById('achievementModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeModal();
            }
        });

        function closeModal() {
            const modal = document.getElementById('achievementModal');
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
            currentAchievementId = null;

            // Refresh achievements to update stats
            loadAchievements();
        }

        // Open image in fullscreen (simple implementation)
        function openImageFullscreen(imageSrc) {
            const fullscreenDiv = document.createElement('div');
            fullscreenDiv.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                z-index: 2000;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imageSrc;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                object-fit: contain;
            `;

            fullscreenDiv.appendChild(img);
            fullscreenDiv.onclick = () => document.body.removeChild(fullscreenDiv);
            document.body.appendChild(fullscreenDiv);
        }
    </script>
</body>
</html> 